# using Crystalline

# println("Crystal structure: ", S"x,-y,-z")

# sg = spacegroup(16, Val(3))

# println("Space group: ", sg)


using CrystalInfoFramework, FilePaths


cif_file = Cif(p"C:\Users\<USER>\Documents\Repos\julia\crystal-symmetry-seeker\examples\ZnS-Sfaleryt.cif")


for (key, value) in cif_file
  print(key)
  global my_block::Dict{String, Any} = cif_file[key]
end

# Access data within the block
symmetry::Vector{String} = my_block["_symmetry_equiv_pos_as_xyz"]
labels::Vector{String} = my_block["_atom_site_label"]
type_symbols::Vector{String} = my_block["_atom_site_type_symbol"]
symmetry_multiplicities::Vector{String} = my_block["_atom_site_symmetry_multiplicity"]
Wyckoff_symbols::Vector{String} = my_block["_atom_site_wyckoff_symbol"]
fract_xs::Vector{String} = my_block["_atom_site_fract_x"]
fract_ys::Vector{String} = my_block["_atom_site_fract_y"]
fract_zs::Vector{String} = my_block["_atom_site_fract_z"]
occupancies::Vector{String} = my_block["_atom_site_occupancy"]
calc_flag::Vector{String} = my_block["_atom_site_calc_flag"]

